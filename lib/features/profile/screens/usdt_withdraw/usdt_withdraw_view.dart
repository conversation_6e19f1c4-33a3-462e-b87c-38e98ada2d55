import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';

import 'usdt_withdraw_cubit.dart';

class UsdtWithdrawView extends StatelessWidget {
  const UsdtWithdrawView({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = BlocProvider.of<UsdtWithdrawCubit>(context);

    return Scaffold(
        body: SingleChildScrollView(
      child: Column(
        children: [
          ShadowBox(
            child: Column(
              children: [
                Text('USDT Withdraw'),
              ],
            ),
          ),
        ],
      ),
    ));
  }
}
